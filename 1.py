"""
MessageFilterAgent Demo - AutoGen AgentChat

This demo shows how to use MessageFilterAgent to filter messages in a multi-agent workflow.
The MessageFilterAgent wraps another agent and only passes specific messages based on filtering rules.

Requirements:
pip install autogen-agentchat[openai]
"""

import asyncio
from typing import List, Sequence
from autogen_agentchat.agents import AssistantAgent, MessageFilterAgent, BaseChatAgent
from autogen_agentchat.messages import TextMessage, BaseChatMessage
from autogen_agentchat.base import Response
from autogen_agentchat.agents import MessageFilterConfig, PerSourceFilter
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ChatCompletionClient
from autogen_core import CancellationToken

def get_llm(model_name, api_key=""):
    """获取语言模型客户端"""
    model_config = {
        "text_models": {
            "deepseek-v3-friday": {
                "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
                "config": {
                    "model": "deepseek-v3-friday",
                    "api_key": "YOUR_API_KEY_HERE",
                    "base_url": "https://aigc.sankuai.com/v1/openai/native",
                    "model_info": {
                        "vision": False,
                        "function_calling": True,
                        "json_output": False,
                        "family": "unknown",
                        "structured_output": False,
                        "multiple_system_messages": True
                    },
                    "max_tokens": 8192
                }
            }
        }
    }
    if api_key:
        model_config["text_models"][model_name]["config"]["api_key"] = api_key
    model_client = ChatCompletionClient.load_component(model_config["text_models"][model_name])
    return model_client


# 创建一个简单的模拟代理用于演示
class MockAgent(BaseChatAgent):
    """简单的模拟代理，用于演示 MessageFilterAgent"""
    
    def __init__(self, name: str, description: str = "A mock agent for testing"):
        super().__init__(name=name, description=description)
        self.message_history: List[BaseChatMessage] = []
    
    @property
    def produced_message_types(self) -> Sequence[type[BaseChatMessage]]:
        return [TextMessage]
    
    async def on_messages(self, messages: Sequence[BaseChatMessage], cancellation_token: CancellationToken) -> Response:
        # 存储收到的消息用于分析
        self.message_history.extend(messages)
        
        # 创建响应内容，显示收到的消息信息
        received_info = []
        for msg in messages:
            if isinstance(msg, TextMessage):
                received_info.append(f"从 '{msg.source}' 收到: {msg.content[:50]}...")
        
        response_content = f"[{self.name}] 处理了 {len(messages)} 条消息:\n" + "\n".join(received_info)
        response_message = TextMessage(content=response_content, source=self.name)
        
        return Response(chat_message=response_message)
    
    async def on_reset(self, cancellation_token: CancellationToken) -> None:
        self.message_history.clear()


async def demo_basic_message_filtering():
    """演示基本的消息过滤功能"""
    print("=== 基本消息过滤演示 ===\n")
    
    # 创建内部代理
    inner_agent = MockAgent("inner_agent", "处理过滤后消息的内部代理")
    
    # 配置过滤器：只接收来自 "user" 和 "assistant" 的消息，每个来源最多保留最后 2 条
    filter_config = MessageFilterConfig(
        per_source=[
            PerSourceFilter(source="user", position="last", count=2),
            PerSourceFilter(source="assistant", position="last", count=1),
        ]
    )
    
    # 创建 MessageFilterAgent
    filter_agent = MessageFilterAgent(
        name="filter_agent",
        wrapped_agent=inner_agent,
        filter=filter_config
    )
    
    # 创建一系列测试消息
    test_messages = [
        TextMessage(content="第一条用户消息", source="user"),
        TextMessage(content="第二条用户消息", source="user"), 
        TextMessage(content="第三条用户消息", source="user"),
        TextMessage(content="助手回复", source="assistant"),
        TextMessage(content="系统消息", source="system"),  # 这条会被过滤掉
        TextMessage(content="第四条用户消息", source="user"),
    ]
    
    print("发送的消息序列：")
    for i, msg in enumerate(test_messages, 1):
        print(f"{i}. [{msg.source}]: {msg.content}")
    
    # 发送消息到过滤代理
    response = await filter_agent.on_messages(test_messages, CancellationToken())
    
    print(f"\n过滤代理的响应：\n{response.chat_message.content}")
    
    # 显示内部代理实际收到的消息
    print(f"\n内部代理实际收到的消息数量: {len(inner_agent.message_history)}")
    for i, msg in enumerate(inner_agent.message_history, 1):
        if isinstance(msg, TextMessage):
            print(f"{i}. [{msg.source}]: {msg.content}")


async def demo_multi_agent_workflow():
    """演示多代理工作流中的消息过滤"""
    print("\n\n=== 多代理工作流演示 ===\n")
    
    # 创建多个内部代理
    writer_agent = MockAgent("writer", "专门写作的代理")
    editor_agent = MockAgent("editor", "专门编辑的代理")
    reviewer_agent = MockAgent("reviewer", "专门审查的代理")
    
    # 为每个代理配置不同的过滤器
    
    # 写作者只看用户的原始请求和编辑的最新反馈
    writer_filter = MessageFilterAgent(
        name="filtered_writer",
        wrapped_agent=writer_agent,
        filter=MessageFilterConfig(per_source=[
            PerSourceFilter(source="user", position="first", count=1),
            PerSourceFilter(source="editor", position="last", count=1),
        ])
    )
    
    # 编辑者只看用户请求和写作者的最新作品
    editor_filter = MessageFilterAgent(
        name="filtered_editor", 
        wrapped_agent=editor_agent,
        filter=MessageFilterConfig(per_source=[
            PerSourceFilter(source="user", position="first", count=1),
            PerSourceFilter(source="writer", position="last", count=1),
        ])
    )
    
    # 审查者看到用户请求、最新的写作和编辑结果
    reviewer_filter = MessageFilterAgent(
        name="filtered_reviewer",
        wrapped_agent=reviewer_agent,
        filter=MessageFilterConfig(per_source=[
            PerSourceFilter(source="user", position="first", count=1),
            PerSourceFilter(source="writer", position="last", count=1), 
            PerSourceFilter(source="editor", position="last", count=1),
        ])
    )
    
    # 模拟工作流消息序列
    messages = [
        TextMessage(content="请写一篇关于AI的短文", source="user"),
        TextMessage(content="AI是未来的技术...", source="writer"),
        TextMessage(content="建议增加具体例子", source="editor"),
        TextMessage(content="AI是未来的技术，比如自动驾驶...", source="writer"),
        TextMessage(content="语法需要完善", source="editor"), 
        TextMessage(content="AI是未来的重要技术，例如自动驾驶汽车...", source="writer"),
    ]
    
    print("完整的消息历史：")
    for i, msg in enumerate(messages, 1):
        print(f"{i}. [{msg.source}]: {msg.content}")
    
    # 测试每个过滤代理收到什么消息
    agents = [
        ("写作者", writer_filter),
        ("编辑者", editor_filter), 
        ("审查者", reviewer_filter)
    ]
    
    for agent_name, agent in agents:
        print(f"\n--- {agent_name}代理处理结果 ---")
        response = await agent.on_messages(messages, CancellationToken())
        print(response.chat_message.content)


async def demo_position_filtering():
    """演示位置过滤功能（first, last）"""
    print("\n\n=== 位置过滤演示 ===\n")
    
    inner_agent = MockAgent("position_test_agent")
    
    # 测试不同的位置过滤配置
    configs = [
        ("只要第一条", MessageFilterConfig(per_source=[
            PerSourceFilter(source="user", position="first", count=1)
        ])),
        ("只要最后一条", MessageFilterConfig(per_source=[
            PerSourceFilter(source="user", position="last", count=1) 
        ])),
        ("前两条", MessageFilterConfig(per_source=[
            PerSourceFilter(source="user", position="first", count=2)
        ])),
        ("后两条", MessageFilterConfig(per_source=[
            PerSourceFilter(source="user", position="last", count=2)
        ])),
        ("所有消息", MessageFilterConfig(per_source=[
            PerSourceFilter(source="user")  # position=None, count=None 表示全部
        ])),
    ]
    
    # 准备测试消息
    test_messages = [
        TextMessage(content="消息1", source="user"),
        TextMessage(content="消息2", source="user"),
        TextMessage(content="消息3", source="user"),
        TextMessage(content="消息4", source="user"),
        TextMessage(content="消息5", source="user"),
    ]
    
    print("测试消息序列：")
    for i, msg in enumerate(test_messages, 1):
        print(f"{i}. {msg.content}")
    
    for config_name, config in configs:
        print(f"\n--- {config_name} ---")
        filter_agent = MessageFilterAgent(
            name="test_agent",
            wrapped_agent=inner_agent, 
            filter=config
        )
        
        # 重置内部代理
        await inner_agent.on_reset(CancellationToken())
        
        response = await filter_agent.on_messages(test_messages, CancellationToken())
        print(response.chat_message.content)


async def demo_with_real_llm():
    """使用真实的LLM演示MessageFilterAgent（需要OpenAI API key）"""
    print("\n\n=== 真实LLM演示 ===\n")
    
    try:
        # 注意：需要设置 OPENAI_API_KEY 环境变量
        api_key = "1932375702800756788"
        model_client = get_llm("deepseek-v3-friday", api_key)
        
        # 创建助手代理
        assistant = AssistantAgent(
            name="assistant",
            model_client=model_client,
            system_message="你是一个有用的助手，会根据收到的消息提供帮助。"
        )
        
        # 配置过滤器：只保留用户的最后一条消息
        filter_config = MessageFilterConfig(per_source=[
            PerSourceFilter(source="user", position="last", count=1)
        ])
        
        filtered_assistant = MessageFilterAgent(
            name="filtered_assistant",
            wrapped_agent=assistant,
            filter=filter_config
        )
        
        # 创建测试对话
        messages = [
            TextMessage(content="我想学习Python编程", source="user"),
            TextMessage(content="具体我想了解函数的使用", source="user"),
            TextMessage(content="特别是lambda函数", source="user"),
        ]
        
        print("发送的消息：")
        for i, msg in enumerate(messages, 1):
            print(f"{i}. [{msg.source}]: {msg.content}")
        
        print("\n助手响应：")
        response = await filtered_assistant.on_messages(messages, CancellationToken())
        print(response.chat_message.content)
        
    except Exception as e:
        print(f"LLM演示失败（可能需要设置API key）: {e}")
        print("请设置 OPENAI_API_KEY 环境变量后重试")


async def main():
    """运行所有演示"""
    print("MessageFilterAgent 演示程序")
    print("=" * 50)
    
    # 运行各种演示
    await demo_basic_message_filtering()
    await demo_multi_agent_workflow() 
    await demo_position_filtering()
    await demo_with_real_llm()
    
    print("\n演示完成！")


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())