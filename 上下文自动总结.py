# -*- coding: utf-8 -*-
"""
上下文自动总结示例程序

本程序演示了如何使用 autogen 框架中的自动总结功能，
通过缓冲总结上下文构建器来自动管理和总结对话历史。
"""

import asyncio
from pprint import pprint
from autogen_ext.models.replay import ReplayChatCompletionClient
from autogen_agentchat.agents import AssistantAgent
from autogen_core import CancellationToken

from autogen_contextplus.extension.context import (
    buffered_summary_chat_completion_context_builder,
)


async def main() -> None:
    """
    主函数 - 演示上下文自动总结功能

    本函数展示了以下功能：
    1. 创建带有自动总结功能的上下文
    2. 运行多个任务并观察总结效果
    3. 代理重置和状态恢复
    """
    # 创建主要的聊天完成客户端，预设回复内容
    client = ReplayChatCompletionClient(
        chat_completions=[
            "paris",    # 法国首都的回复
            "seoul",    # 韩国首都的回复
            "paris",    # 重复的法国首都回复
            "seoul",    # 重复的韩国首都回复
        ]
    )

    # 创建专门用于总结的客户端
    client_summary = ReplayChatCompletionClient(
        chat_completions=[
            "SUMMARY",  # 模拟的总结内容
        ]
    )

    # 创建带有自动总结功能的上下文构建器
    context = buffered_summary_chat_completion_context_builder(
        max_messages=4,  # 最大消息数量，超过时触发总结
        summary_start=1,  # 总结开始位置（从第1条消息开始）
        summary_end=-1,   # 总结结束位置（-1表示到倒数第1条消息）
        model_client=client_summary,  # 用于总结的模型客户端
        system_message="总结到目前为止的对话内容以供记忆",  # 总结系统消息（中文）
        summary_format="对话的这一部分已总结如下：{summary}",  # 总结格式（中文）
    )

    # 创建助手代理
    agent = AssistantAgent(
        "helper",  # 代理名称
        model_client=client,  # 主要模型客户端
        system_message="你是一个有用的助手",  # 系统消息（中文）
        model_context=context  # 带有自动总结功能的上下文
    )

    # 第一轮测试：询问法国首都
    print("=== 第一轮测试：询问法国首都 ===")
    await agent.run(task="法国的首都是什么？")
    res = await context.get_messages()
    print(f"[结果] 消息内容:")
    pprint(res)
    print(f"[结果] 上下文长度: {len(res)}, 上下文类型: {type(context)}")

    # 第二轮测试：询问韩国首都
    print("\n=== 第二轮测试：询问韩国首都 ===")
    await agent.run(task="韩国的首都是什么？")
    res = await context.get_messages()
    print(f"[结果] 消息内容:")
    pprint(res)
    print(f"[结果] 上下文长度: {len(res)}, 上下文类型: {type(context)}")

    print("\n" + "="*50)
    print("=== 代理重置和状态恢复测试 ===")

    # 重置代理并保存/恢复状态
    cancellation_token = CancellationToken()
    await agent.on_reset(cancellation_token=cancellation_token)  # 重置代理
    test = agent.dump_component()  # 导出代理状态
    agent = AssistantAgent.load_component(test)  # 从状态恢复代理
    new_context = agent.model_context  # 获取新的上下文对象

    # 重置后的第一轮测试：询问法国首都
    print("\n=== 重置后第一轮：询问法国首都 ===")
    await agent.run(task="法国的首都是什么？")
    res = await new_context.get_messages()
    print(f"[结果] 消息内容:")
    pprint(res)
    print(f"[结果] 上下文长度: {len(res)}, 上下文类型: {type(new_context)}")

    # 重置后的第二轮测试：询问韩国首都
    print("\n=== 重置后第二轮：询问韩国首都 ===")
    await agent.run(task="韩国的首都是什么？")
    res = await new_context.get_messages()
    print(f"[结果] 消息内容:")
    pprint(res)
    print(f"[结果] 上下文长度: {len(res)}, 上下文类型: {type(new_context)}")

if __name__ == "__main__":
    """
    程序入口点

    运行主函数来演示上下文自动总结功能。
    这个示例展示了如何在 autogen 框架中使用缓冲总结上下文构建器
    来自动管理对话历史，当消息数量超过限制时自动进行总结。
    """
    asyncio.run(main())