# -*- coding: utf-8 -*-
"""
代理工具示例程序

本程序演示了如何在 autogen 框架中使用 AgentTool，
将一个代理包装成工具供另一个代理使用，实现代理间的协作。
"""

import asyncio

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.tools import AgentTool
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ChatCompletionClient


def get_llm(model_name: str, api_key: str = "") -> ChatCompletionClient:
    """
    获取语言模型客户端

    Args:
        model_name: 模型名称
        api_key: API密钥

    Returns:
        ChatCompletionClient: 配置好的模型客户端
    """
    model_config = {
        "text_models": {
            "deepseek-v3-friday": {
                "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
                "config": {
                    "model": "deepseek-v3-friday",
                    "api_key": "YOUR_API_KEY_HERE",
                    "base_url": "https://aigc.sankuai.com/v1/openai/native",
                    "model_info": {
                        "vision": False,
                        "function_calling": True,
                        "json_output": False,
                        "family": "unknown",
                        "structured_output": False,
                        "multiple_system_messages": True
                    },
                    "max_tokens": 8192
                }
            }
        }
    }
    if api_key:
        model_config["text_models"][model_name]["config"]["api_key"] = api_key
    model_client = ChatCompletionClient.load_component(model_config["text_models"][model_name])
    return model_client


async def main() -> None:
    """
    主函数 - 演示代理工具功能

    本函数展示了以下功能：
    1. 创建一个写作代理
    2. 将写作代理包装成工具
    3. 创建一个主助手代理，使用写作工具
    4. 通过控制台界面运行流式对话
    """

    # 获取模型客户端（使用自定义配置）
    api_key = "1932375702800756788"  # 替换为您的实际API密钥
    model_client = get_llm("deepseek-v3-friday", api_key)

    # 创建写作代理
    # 这个代理专门负责文本生成任务
    writer = AssistantAgent(
        name="writer",  # 代理名称
        description="专门用于生成文本的写作代理。",  # 代理描述（中文）
        model_client=model_client,  # 模型客户端
        system_message="你是一个优秀的写作助手，能够创作高质量的文本内容。",  # 系统消息（中文）
    )

    # 将写作代理包装成工具
    # 这样其他代理就可以调用这个写作代理作为工具使用
    writer_tool = AgentTool(agent=writer)

    # 为主代理创建模型客户端（禁用并行工具调用）
    # 禁用并行工具调用可以确保工具按顺序执行，避免冲突
    main_model_client = get_llm("deepseek-v3-friday", api_key)

    # 创建主助手代理
    # 这个代理可以使用写作工具来完成复杂任务
    assistant = AssistantAgent(
        name="assistant",  # 代理名称
        model_client=main_model_client,  # 模型客户端
        tools=[writer_tool],  # 可用工具列表
        system_message="你是一个有用的助手，可以使用写作工具来帮助用户创作内容。",  # 系统消息（中文）
    )

    # 通过控制台界面运行流式对话
    # 任务：写一首关于大海的诗（中文任务）
    await Console(assistant.run_stream(task="请写一首关于大海的诗。"))


if __name__ == "__main__":
    """
    程序入口点

    运行主函数来演示代理工具功能。
    这个示例展示了如何将一个代理包装成工具，
    供另一个代理使用，实现代理间的协作。
    """
    asyncio.run(main())
