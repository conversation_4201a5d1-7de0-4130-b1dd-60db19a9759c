import yaml
import os
from autogen_core.models import ChatCompletionClient

def get_llm(model_name, api_key=""):
    model_config = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model_config.yaml")
    with open(model_config, "r") as f:
        model_config = yaml.safe_load(f)
    if api_key:
        model_config["text_models"][model_name]["config"]["api_key"] = api_key
    model_client = ChatCompletionClient.load_component(model_config["text_models"][model_name])
    return model_client