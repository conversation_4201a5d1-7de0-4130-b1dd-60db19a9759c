# 顺序流程控制示例：演示如何创建一个顺序执行的多智能体工作流（A -> B -> C）
# 功能：创建三个智能体，按顺序执行任务，实现从助手回答到中文翻译再到英文翻译的流程

import asyncio

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.conditions import MaxMessageTermination
from autogen_agentchat.teams import DiGraphBuilder, GraphFlow
from autogen_core.models import ChatCompletionClient

def get_llm(model_name, api_key=""):
    """获取语言模型客户端"""
    model_config = {
        "text_models": {
            "deepseek-v3-friday": {
                "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
                "config": {
                    "model": "deepseek-v3-friday",
                    "api_key": "YOUR_API_KEY_HERE",
                    "base_url": "https://aigc.sankuai.com/v1/openai/native",
                    "model_info": {
                        "vision": False,
                        "function_calling": True,
                        "json_output": False,
                        "family": "unknown",
                        "structured_output": False,
                        "multiple_system_messages": True
                    },
                    "max_tokens": 8192
                }
            }
        }
    }
    if api_key:
        model_config["text_models"][model_name]["config"]["api_key"] = api_key
    model_client = ChatCompletionClient.load_component(model_config["text_models"][model_name])
    return model_client

async def main():
    # 使用 OpenAI 模型客户端初始化智能体
    api_key = "YOUR_API_KEY_HERE"
    model_client = get_llm("deepseek-v3-friday", api_key)
    agent_a = AssistantAgent("A", model_client=model_client, system_message="你是一个有用的助手。")
    agent_b = AssistantAgent("B", model_client=model_client, system_message="将输入内容翻译成中文。")
    agent_c = AssistantAgent("C", model_client=model_client, system_message="将输入内容翻译成英文。")

    # 创建顺序流程的有向图 A -> B -> C
    builder = DiGraphBuilder()
    builder.add_node(agent_a).add_node(agent_b).add_node(agent_c)
    builder.add_edge(agent_a, agent_b).add_edge(agent_b, agent_c)
    graph = builder.build()

    # 使用有向图创建 GraphFlow 团队
    team = GraphFlow(
        participants=[agent_a, agent_b, agent_c],
        graph=graph,
        termination_condition=MaxMessageTermination(5),
    )

    # 运行团队并打印事件
    async for event in team.run_stream(task="写一个关于猫的短故事。"):
        print(event)
        print()


asyncio.run(main())
