# -*- coding: utf-8 -*-
"""
上下文控制示例程序

本程序演示了如何使用 autogen 框架中的上下文控制功能，
包括消息缓冲、上下文修改器和代理重置等功能。
"""

import asyncio
from pprint import pprint
from typing import List
from autogen_core.models import UserMessage, AssistantMessage
from autogen_ext.models.replay import ReplayChatCompletionClient
from autogen_ext.models.anthropic import AnthropicChatCompletionClient
from autogen_agentchat.agents import AssistantAgent
from autogen_core import CancellationToken
from autogen_core.model_context import BufferedChatCompletionContext

from autogen_contextplus.conditions import (
    MaxMessageCondition
)
from autogen_contextplus.base.types import (
    ModifierFunction,
)
from autogen_contextplus import (
    ContextPlusChatCompletionContext
)
from autogen_core.models import LLMMessage


def buffered_summary(
    messages: List[LLMMessage],
    non_summarized_messages: List[LLMMessage],
) -> List[LLMMessage]:
    """
    缓冲摘要函数

    对消息列表进行摘要处理，只保留最后3条消息。
    这是一个简单的上下文修改器函数，用于控制上下文长度。

    Args:
        messages: 当前的消息列表
        non_summarized_messages: 未摘要的消息列表

    Returns:
        List[LLMMessage]: 处理后的消息列表（最多3条）
    """
    if len(messages) > 3:
        return messages[-3:]  # 只保留最后3条消息
    return messages


async def main():
    """
    主函数 - 演示上下文控制功能

    本函数展示了以下功能：
    1. 创建带有上下文控制的代理
    2. 运行多个任务并观察上下文变化
    3. 代理重置和状态恢复
    """

    # 创建一个模拟的聊天完成客户端，预设回复内容
    client = ReplayChatCompletionClient(
        chat_completions=[
            "paris",    # 法国首都的回复
            "seoul",    # 韩国首都的回复
            "paris",    # 重复的法国首都回复
            "seoul",    # 重复的韩国首都回复
        ]
    )

    # 创建带有上下文修改器的上下文对象
    # 当消息数量超过2条时，会触发 buffered_summary 函数进行摘要
    context = ContextPlusChatCompletionContext(
        modifier_func=buffered_summary,  # 上下文修改函数
        modifier_condition=MaxMessageCondition(max_messages=2)  # 触发条件：最多2条消息
    )

    # 创建助手代理
    agent = AssistantAgent(
        "helper",  # 代理名称
        model_client=client,  # 模型客户端
        system_message="你是一个有用的助手",  # 系统消息（中文）
        model_context=context  # 上下文对象
    )

    # 第一轮测试：询问法国首都
    print("=== 第一轮测试：询问法国首都 ===")
    await agent.run(task="法国的首都是什么？")
    res = await context.get_messages()
    print(f"[结果] 消息内容:")
    pprint(res)
    print(f"[结果] 上下文长度: {len(res)}, 上下文类型: {type(context)}")

    # 第二轮测试：询问韩国首都
    print("\n=== 第二轮测试：询问韩国首都 ===")
    await agent.run(task="韩国的首都是什么？")
    res = await context.get_messages()
    print(f"[结果] 消息内容:")
    pprint(res)
    print(f"[结果] 上下文长度: {len(res)}, 上下文类型: {type(context)}")

    print("\n" + "="*50)
    print("=== 代理重置和状态恢复测试 ===")

    # 重置代理并保存/恢复状态
    cancellation_token = CancellationToken()
    await agent.on_reset(cancellation_token=cancellation_token)  # 重置代理
    test = agent.dump_component()  # 导出代理状态
    agent = AssistantAgent.load_component(test)  # 从状态恢复代理
    context = agent.model_context  # 获取新的上下文对象

    # 重置后的第一轮测试：询问法国首都
    print("\n=== 重置后第一轮：询问法国首都 ===")
    await agent.run(task="法国的首都是什么？")
    res = await context.get_messages()
    print(f"[结果] 消息内容:")
    pprint(res)
    print(f"[结果] 上下文长度: {len(res)}, 上下文类型: {type(context)}")

    # 重置后的第二轮测试：询问韩国首都
    print("\n=== 重置后第二轮：询问韩国首都 ===")
    await agent.run(task="韩国的首都是什么？")
    res = await context.get_messages()
    print(f"[结果] 消息内容:")
    pprint(res)
    print(f"[结果] 上下文长度: {len(res)}, 上下文类型: {type(context)}")
    
if __name__ == "__main__":
    """
    程序入口点

    运行主函数来演示上下文控制功能。
    这个示例展示了如何在 autogen 框架中使用上下文修改器
    来控制对话历史的长度和内容。
    """
    asyncio.run(main())